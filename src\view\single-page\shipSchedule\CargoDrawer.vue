<template>
  <Drawer
    :title="drawerTitle"
    v-model="visible"
    width="520"
    :mask-closable="false"
    :styles="drawerStyles"
  >
    <Form :model="formData" :rules="formRules" :label-width="100" ref="cargoForm">
      <FormItem label="船舶" prop="ship_id" :rules="formData.is_dock_repair === '0' ? {} : { required: true, message: '请选择船舶', trigger: 'change' }">
        <Select filterable clearable v-model="formData.ship_id" placeholder="请选择船舶" @on-change="shipChange">
          <Option v-for="(item, n) in shipList" :value="item.ship_id" :key="n">{{ item.ship_name }}</Option>
        </Select>
      </FormItem>

      <FormItem label="航次号">
        <Input v-model="formData.voyage_no" :rows="4" placeholder="请输入航次号" >
          <Button type="primary" slot="append" style="color: #fff;" @click="voyageCheck">校正</Button>
        </Input>
      </FormItem>

      <FormItem label="是否坞修" prop="is_dock_repair">
        <Select v-model="formData.is_dock_repair" placeholder="请选择坞修情况">
          <Option value="0">否</Option>
          <Option value="1">是</Option>
        </Select>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="货品" prop="goods_id">
        <Select filterable v-model="formData.goods_id" placeholder="请选择货品">
          <Option v-for="(item, n) in goodsList" :value="item.id" :key="n">{{ item.cargo_name }}</Option>
        </Select>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="货量" prop="amounts">
        <InputNumber v-model="formData.amounts" style="width: 150px" @on-change="getFee"></InputNumber>
        <span style="margin-left: 8px">吨</span>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="运价" prop="freight_rate">
        <InputNumber v-model="formData.freight_rate" style="width: 150px" @on-change="getFee"></InputNumber>
        <span style="margin-left: 8px">元/吨</span>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="运费" prop="shipping_fee">
        <InputNumber disabled v-model="formData.shipping_fee" :min="1" style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">元</span>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="装货港" prop="load_port_id">
        <Select filterable v-model="formData.load_port_id" placeholder="请选择装货港" @on-change="loadPortChange">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="卸货港" prop="unload_port_id">
        <Select filterable v-model="formData.unload_port_id" placeholder="请选择卸货港" @on-change="unloadPortChange">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>

      <FormItem v-if="visible" label="归属月份" prop="belong_month">
        <DatePicker type="month" v-model="formData.belong_month" placeholder="选择归属月份" style="width: 300px" @on-change="data => formData.belong_month = data"></DatePicker>
      </FormItem>

      <FormItem v-if="visible" :label="formData.is_dock_repair === '0' ? '受载开始' : '坞修开始'" prop="start_plan_date">
        <DatePicker type="date" v-model="formData.start_plan_date" placeholder="选择受载开始时间" style="width: 300px" @on-change="startPlanDateChange"></DatePicker>
      </FormItem>

      <FormItem v-if="visible && formData.is_dock_repair === '0'" :label="formData.is_dock_repair === '0' ? '受载结束' : '坞修结束'" prop="end_plan_date">
        <DatePicker type="date" v-model="formData.end_plan_date" placeholder="选择受载结束时间" style="width: 300px" @on-change="endPlanDateChange"></DatePicker>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="重载耗时" prop="carrier_hours">
        <Input v-model="formData.carrier_hours" style="width: 300px" @on-change="carrierHoursChange">
          <div slot="append">
            <span style="margin-left: 6px">小时</span>
            <Button type="primary" style="margin-left: 15px;color: #fff;" @click="correctEstimatedOverDay">校正</Button>
          </div>
        </Input>
      </FormItem>

      <FormItem label="结束时间" prop="estimated_over_day" :rules="{required: true, message: '请选择承运结束时间', trigger: 'change'}">
        <DatePicker disabled type="date" v-model="estimated_over_day" format="yyyy-MM-dd" placeholder="选择承运结束时间" style="width: 300px" @on-change="estimatedOverDayChange"></DatePicker>
        <Tooltip v-if="isEdit && !isRealEnd" placement="left">
          <div slot="content">
            结束时间计算不匹配，此处特殊处理
          </div>
          <Icon class="estimated-over-day-tip" type="md-warning" color="red" size="14"/>
        </Tooltip>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="托运方">
        <Select filterable v-model="formData.shipper" placeholder="请输入联系人姓名">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
        <!-- <Input v-model="formData.shipper" placeholder="请输入联系人姓名" /> -->
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="收货方">
        <Select filterable v-model="formData.recipient" placeholder="请输入联系电话">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
        <!-- <Input v-model="formData.recipient" placeholder="请输入联系电话" /> -->
      </FormItem>

      <FormItem label="备注说明">
        <Input v-model="formData.remarks" type="textarea" :rows="4" placeholder="请输入备注说明" />
      </FormItem>

      <div class="drawer-footer">
        <Button @click="handleCancel" style="margin-right: 8px">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitting">提交</Button>
      </div>
    </Form>
  </Drawer>
</template>

<script>
import dayjs from 'dayjs'
import API from '@/api/shipSchedule'
import { queryCustomerList, queryPortList, queryBasicCargoList } from '@/api/basicData'

export default {
  name: 'CargoDrawer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    cargoData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      estimated_over_day: null,
      shipList: [], // 船舶列表
      goodsList: [], // 货品列表
      portNameList: [], // 港口列表
      agentList: [], // 货主列表
      visible: false,
      submitting: false,
      isRealEnd: true, // 校验结束时间是不是等于受载结束 + 重载耗时
      formData: {
        ship_id_plan: '',
        ship_id: '',
        voyage_no: '',
        belong_month: '',
        goods_id: '',
        is_dock_repair: '0',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        carrier_hours: 0,
        estimated_over_day: '',
        shipper: '',
        recipient: '',
        remarks: ''
      },
      formRules: {
        goods_id: [
          { required: true, message: '请选择货物类型', trigger: 'change' }
        ],
        amounts: [
          {
            required: true,
            message: '请输入货物数量',
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback()
              } else {
                callback(new Error('请输入货物数量'))
              }
            }
          }
        ],
        freight_rate: [
          {
            required: true,
            message: '请输入运价',
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback()
              } else {
                callback(new Error('请输入运价'))
              }
            }
          }
        ],
        load_port_id: [
          { required: true, message: '请输入装货港', trigger: 'change' }
        ],
        unload_port_id: [
          { required: true, message: '请输入卸货港', trigger: 'change' }
        ],
        belong_month: [
          { required: true, message: '请选择归属月份', trigger: 'change' }
        ],
        start_plan_date: [
          { required: true, message: '请选择受载开始时间', trigger: 'change' }
        ],
        end_plan_date: [
          { required: true, message: '请选择受载结束时间', trigger: 'change' }
        ]
        // estimated_over_day: [
        //   { required: true, message: '请选择承运结束时间', trigger: 'change' }
        // ]
      },
      drawerStyles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      }
    }
  },
  computed: {
    drawerTitle () {
      return this.isEdit ? '编辑货源信息' : '添加新货源'
    }
  },
  watch: {
    value (val) {
      this.visible = val
    },
    visible (val) {
      this.$emit('input', val)
      if (!val) {
        this.resetForm()
      } else {
        this.getBaseData()
      }
    },
    cargoData: {
      handler (val) {
        this.resetForm()
        if (val && Object.keys(val).length > 0) {
          this.estimated_over_day = val.estimated_over_day
          this.formData = {
            ship_id_plan: val.ship_id_plan || '',
            ship_id: val.ship_id || '',
            voyage_no: val.voyage_no || '',
            belong_month: val.belong_month,
            goods_id: val.goods_id || '',
            is_dock_repair: val.is_dock_repair || '0',
            amounts: parseInt(val.amounts.replace(/,/g, '')) || 0,
            freight_rate: parseInt(val.freight_rate.replace(/,/g, '')) || 0,
            shipping_fee: parseInt(val.shipping_fee.replace(/,/g, '')) || 0,
            load_port_id: val.load_port_id || '',
            unload_port_id: val.unload_port_id || '',
            start_plan_date: val.start_plan_date,
            end_plan_date: val.end_plan_date,
            carrier_hours: val.carrier_hours,
            estimated_over_day: val.estimated_over_day,
            source: val.source || '',
            shipper: val.shipper || '',
            recipient: val.recipient || '',
            remarks: val.remarks || ''
          }
          if (this.isEdit) {
            Object.assign(this.formData, { voyage_month_plan_id: val.voyage_month_plan_id })
            let calcDate = this.formData.source === '1' ? this.formData.start_plan_date : this.formData.end_plan_date
            this.isRealEnd = this.estimated_over_day === dayjs(calcDate).add(Math.ceil(this.formData.carrier_hours / 24), 'day').format('YYYY-MM-DD')
          }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取基础信息
    getBaseData () {
      this.shipList = JSON.parse(localStorage.getItem('shipNameList')).filter(list => list.business_model === '1').filter(t => !t.ship_name.includes('善')) || []
      // 获取港口
      queryPortList(this.loadPortListQuery).then(res => {
        if (res.data.Code === 10000) {
          this.portNameList = res.data.Result
        }
      })
      // 获取货物
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.goodsList = res.data.Result
        }
      })
      // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
      queryCustomerList({ company_type: 2 }).then(res => {
        this.agentList = []
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.agentList.push(item)
          })
        }
      })
    },
    // 校正航次号
    voyageCheck () {
      let _param = {
        voyage_month_plan_id: this.formData.voyage_month_plan_id,
        voyage_no: this.formData.voyage_no
      }
      API.updateVoyageMonthPlanVoyageNo(_param).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.visible = false
          this.$emit('checkEnd')
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    shipChange (val) {
      this.estimatedDayCalc()
    },
    estimatedOverDayChange (item) {
      this.estimated_over_day = item
      this.formData.estimated_over_day = item
    },
    // 计算运费
    getFee () {
      let _fee = parseFloat(this.formData.amounts) * parseFloat(this.formData.freight_rate)
      this.formData.shipping_fee = parseFloat(_fee.toFixed(0))
    },
    loadPortChange (e) {
      this.formData.load_port_id = e
      this.estimatedDayCalc()
    },
    unloadPortChange (e) {
      this.formData.unload_port_id = e
      this.estimatedDayCalc()
    },
    startPlanDateChange (e) {
      this.formData.start_plan_date = e
      this.carrierHoursChange()
    },
    endPlanDateChange (e) {
      this.formData.end_plan_date = e
      // 包运时才去计算结束时间
      if (this.formData.source === '1') this.estimatedDayCalc()
    },
    carrierHoursChange () { // 重载耗时变化，自动调整结束时间  结束时间=受载结束+重载耗时/24  向上取天数
      if (this.formData.source === '1') {
        this.estimated_over_day = dayjs(this.formData.start_plan_date).add(Math.ceil(this.formData.carrier_hours / 24), 'day').format('YYYY-MM-DD')
      } else {
        this.estimated_over_day = dayjs(this.formData.end_plan_date).add(Math.ceil(this.formData.carrier_hours / 24), 'day').format('YYYY-MM-DD')
      }
      this.formData.estimated_over_day = this.estimated_over_day
    },
    // 修改承运时长后校正航次结束时间
    correctEstimatedOverDay () {
      // 未绑定船舶提醒
      if (!this.formData.ship_id || this.formData.ship_id === '') {
        this.$Modal.confirm({
          title: '未绑定船舶提醒',
          content: '未绑定船舶，所有船舶该航线会被修正，是否继续？',
          onOk: () => {
            this.$Modal.remove()
            this.correctEstimatedOverdayFun()
          }
        })
      } else {
        this.correctEstimatedOverdayFun()
      }
    },
    correctEstimatedOverdayFun () {
      if (this.formData.load_port_id && this.formData.unload_port_id && this.formData.end_plan_date && this.formData.carrier_hours) { // 如果装货港、卸货港、卸货时间、承运时长都有值
        let _param = {
          port_id_from: this.formData.load_port_id,
          port_id_to: this.formData.unload_port_id,
          end_plan_date: this.formatDate(this.formData.end_plan_date),
          modify_avg_carrier_hours: this.formData.carrier_hours
        }
        if (this.formData.ship_id) {
          Object.assign(_param, {
            ship_id: this.formData.ship_id
          })
        }
        API.correctStatPortToPortCarrierTime(_param).then(res => {
          if (res.data.Code === 10000 && res.data.Result.length > 0) {
            this.estimated_over_day = res.data.Result
            this.formData.estimated_over_day = this.estimated_over_day
            this.$Message.success(res.data.Message)
          } else {
            this.estimated_over_day = null
            this.formData.estimated_over_day = null
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 计算承运结束日期
    estimatedDayCalc (e) {
      if (this.formData.load_port_id && this.formData.unload_port_id && this.formData.end_plan_date) { // 如果装货港、卸货港、卸货时间都有值
        let _param = {
          load_port_id: this.formData.load_port_id,
          unload_port_id: this.formData.unload_port_id,
          end_plan_date: this.formData.source === '1' ? this.formData.start_plan_date : this.formData.end_plan_date
        }
        if (this.formData.ship_id) {
          Object.assign(_param, {
            ship_id: this.formData.ship_id
          })
        }
        API.queryStatLinePortGroupList(_param).then(res => {
          if (res.data.Code === 10000 && res.data.Result.length > 0) {
            this.estimated_over_day = res.data.Result[0].estimated_over_day
            if (this.formData.ship_id !== '') {
              this.formData.carrier_hours = res.data.Result[0].carrier_hours
            } else {
              this.formData.carrier_hours = res.data.Result[0].avg_carrier_hours
            }
            this.formData.estimated_over_day = this.estimated_over_day
            // 如果结束时间发生变化，则把校正移除
            this.isRealEnd = this.estimated_over_day === dayjs(this.formData.end_plan_date).add(Math.ceil(this.formData.carrier_hours / 24), 'day').format('YYYY-MM-DD')
          } else {
            this.estimated_over_day = null
            this.formData.estimated_over_day = null
          }
        })
      }
    },
    handleCancel () {
      this.visible = false
      this.resetForm()
      this.$emit('cancel')
    },
    formatDate (date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    },
    formatMonth (date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
    },
    handleSubmit () {
      this.formData.belong_month = this.formatMonth(this.formData.belong_month)
      this.formData.start_plan_date = this.formatDate(this.formData.start_plan_date)
      this.formData.end_plan_date = this.formatDate(this.formData.end_plan_date)
      this.formData.estimated_over_day = this.formatDate(this.formData.estimated_over_day)
      this.$refs['cargoForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          if (this.isEdit) {
            API.updateVoyageMonthPlan(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.submitting = false
                this.$emit('submit')
                this.visible = false
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          } else {
            API.addVoyageMonthPlan(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.submitting = false
                this.$emit('submit')
                this.visible = false
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    resetForm () {
      this.estimated_over_day = null
      this.formData = {
        ship_id_plan: '',
        ship_id: '',
        voyage_no: '',
        belong_month: '',
        goods_id: '',
        is_dock_repair: '0',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        carrier_hours: 0,
        estimated_over_day: '',
        shipper: '',
        recipient: '',
        remarks: ''
      }
      if (this.$refs.cargoForm) {
        this.$refs.cargoForm.resetFields()
      }
    },
    formatDateRange (dateRange) {
      if (!dateRange || dateRange.length !== 2) return ''
      return `${this.formatDate(dateRange[0])} ~ ${this.formatDate(dateRange[1])}`
    }
  }
}
</script>

<style scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
}
.estimated-over-day-tip {
  margin-left: 10px;
  animation: ani-scale-spin 1s linear infinite;
}
@keyframes ani-scale-spin {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
</style>
